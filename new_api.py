import os
import json
import base64
from io import Bytes<PERSON>
from typing import Optional, Dict, Any, Union
import re
import openai
import async<PERSON>
from concurrent.futures import Thr<PERSON>PoolExecutor
import numpy as np
import cv2

from dotenv import load_dotenv
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import fitz  # PyMuPDF for PDF processing

# Load environment variables
load_dotenv()

# Initialize OpenAI client
client = openai.OpenAI(
    api_key= "none",
    base_url= 'http://localhost:8678/v1'
)

app = FastAPI(title="Vision Language Model API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def encode_image_to_base64(image_file: BytesIO) -> str:
    """Convert an image to base64 encoding"""
    return base64.b64encode(image_file.getvalue()).decode('utf-8')

def detect_incomplete_parts(image_data: BytesIO) -> bool:
    """
    Detect incomplete parts (black blocks) in the image using OpenCV.

    Args:
        image_data: Binary image data

    Returns:
        True if incomplete parts are detected, False otherwise
    """
    try:
        # Convert BytesIO to numpy array
        image_data.seek(0)
        image_bytes = np.frombuffer(image_data.read(), np.uint8)

        # Decode image using OpenCV
        image = cv2.imdecode(image_bytes, cv2.IMREAD_COLOR)

        if image is None:
            return False

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply binary thresholding to detect black areas
        # Pixels with intensity < 30 are considered black (incomplete parts)
        _, binary = cv2.threshold(gray, 30, 255, cv2.THRESH_BINARY)

        # Invert the binary image so black areas become white (foreground)
        binary_inv = cv2.bitwise_not(binary)

        # Find contours of black areas
        contours, _ = cv2.findContours(binary_inv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filter contours by area to ignore small noise
        min_area = 500  # Minimum area threshold for significant black blocks
        significant_black_areas = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                # Get bounding rectangle for the black area
                x, y, w, h = cv2.boundingRect(contour)
                significant_black_areas.append({
                    'area': area,
                    'bounding_box': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)}
                })

        # Return True if any significant black areas are found
        return len(significant_black_areas) > 0

    except Exception as e:
        print(f"Error in black block detection: {str(e)}")
        return False
def extract_json_with_regex(text: str) -> Dict[str, Any]:
    """
    Extract JSON from text using regex.
    This handles cases where the JSON might be embedded in markdown code blocks
    or surrounded by other text.
    """
    # Try to find JSON in markdown code blocks first
    json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
    
    # If not found in code blocks, try to find JSON brackets
    if not json_match:
        json_match = re.search(r'(\{[\s\S]*\})', text)
    
    if json_match:
        json_str = json_match.group(1).strip()
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # Try to clean up the JSON string if it has issues
            # For example, handle trailing commas in objects and arrays
            clean_json = re.sub(r',\s*}', '}', json_str)
            clean_json = re.sub(r',\s*]', ']', clean_json)
            return json.loads(clean_json)
    
    # If no JSON found, raise an exception
    raise ValueError("Could not extract valid JSON from the response")

def process_image(image_data: BytesIO, prompt: str) -> Dict[str, Any]:
    """
    Process an image using the OpenAI VLM and extract JSON from the response.

    Args:
        image_data: Binary image data
        prompt: The prompt to send to the VLM

    Returns:
        Extracted JSON from the VLM response with Remarks field for incomplete parts and missing date stamp
    """
    try:
        # Detect incomplete parts (black blocks) in the image
        has_incomplete_parts = detect_incomplete_parts(BytesIO(image_data.getvalue()))

        # Encode the image
        base64_image = encode_image_to_base64(image_data)

        response = client.chat.completions.create(
            model= 'hf_models/Qwen/Qwen2.5-VL-7B-Instruct',
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            response_format={"type": "json_object"},
            temperature = 0
        )

        # Extract JSON from the response
        result_text = response.choices[0].message.content
        print(result_text)
        try:
            result_json = extract_json_with_regex(result_text)

            # Build remarks based on conditions
            remarks = []
            if has_incomplete_parts:
                remarks.append("incomplete")

            # Check if this is a date stamp extraction and if date is missing
            if "date_on_stamp" in result_json:
                date_on_stamp = result_json.get("date_on_stamp", "")
                if not date_on_stamp or date_on_stamp.strip() == "":
                    remarks.append("Missing Date Stamp")

            # Add Remarks field
            result_json["DrawingRemark"] = ", ".join(remarks) if remarks else ""

            return result_json
        except ValueError as ve:
            # Even if JSON extraction fails, add Remarks field
            result = {"raw_response": result_text, "error": "Failed to extract JSON"}
            remarks = []
            if has_incomplete_parts:
                remarks.append("incomplete")
            result["Remarks"] = ", ".join(remarks) if remarks else ""
            return result

    except json.JSONDecodeError:
        raise HTTPException(
            status_code=500,
            detail="Failed to parse JSON from the VLM response"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing image: {str(e)}"
        )


# Prompt for extracting basic info
STEP1_PROMPT = """ 
**task**
extract project_title, drawing_title, drawing_title_in_drawing, drawing_number from the image in json format without explanation

**field definition**
1. project_title: <string, required, overall name of the construction/development project, extract the whole box of project title in the legend, multiline content>
2. drawing_title: <string, required, specific name of the individual floor plan, extract the whole box of project title in the legend, multiline content, if no drawing title found in legend, leave empty>
3. drawing_title_in_drawing: <Array of string, optional, if there is any underlined title found in the drawings>
4. drawing_number: <string, if any, Unique identifier for tracking revisions, referencing in documents, or linking to schedules>

# Rule
**STRICTLY FOLLOW** the rules to destinguish the project title and drawing title, as various keywords for project or drawing title will be found.
# Common Label used to distinguish **Drawing Title** & **Project Title**
## Drawing Title :  
 - common label: "Title", "DWG"
 
## Project Title:
 - common label: "LOCATION", "JOB"

# Additional rule to distinghuish: 
## Project Title:
  - mostly a location or address 
  - sometime include company name
 
## Drawing Title:
  - mostly described as the system or room the drawing related to

## Title in Drawing Legend & Title inside Drawing
  - one plan file might include multiple drawings, Drawing Title will place inside the Drawing with Underline, but **ALWAYS** use the Title in Drawing Legend as the drawing title first
  - separte extract Drawing Title(s) in Drawing and the Drawing Title in Legend
  

**sample output format**
{
	"project_title": "Oceanview Resort & Spa",
	"drawing_title": "Level 4 – Guest Room Floor Plan",
	"drawing_number": "A-101"
}
"""  
 # Prompt for extracting FSI type
STEP2_PROMPT = """
**task**
look for the keywords in the floor plan, extract id of the type of fsi from the image in json without explanation

**definition**
here is a table of type of FSI and keywords related:

| Type of FSI | Keywords |
|------------------------------------------|--------------------------------------------------------------------------|
| 1 Audio/Visual Advisory System | A.V.A.S., Fire Alarm Bell |
| 2 Automatic Actuating Device | A.A.D.|
| 3 Automatic Fixed Installation other than Water | FM 200, Novec 1230, BTM, BCF|
| 4 Automatic Fixed Installation using Water | A.F.I. (Water), Deluge System, Drencher System, Sprinkler System, Water Mist System, Water Spray System |
| 5 Deluge System| Deluge|
| 6 Drencher System| Drencher|
| 7 Dry Riser System | D.R.S., F.S. Inlet|
| 8 Dust Detection System| D.D.S.|
| 9 Dynamic Smoke Extraction System| D.S.E.S, Fan, Extraction Fan, Damper|
| 10 Emergency Generator| E. Gen|
| 11 Emergency Lighting | E.L.|
| 12 Exit Sign| E.S., Exit|
| 13 Fire Alarm System| Break Glass Unit, B.G.U., F.A.S., Manual Call Point, M.C.P., Alarm Bell |
| 14 Fire Control Centre| F.C.C.|
| 15 Fire Detection System| F.D.S., Detector, Smoke Detector, Heat Detector |
| 16 Fire Hydrant/Hose Reel System| Fire Hydrant, Hydrant, F.H., Hose Reel, H.R., Fixed Fire Pump, Intermediate Fire Pump, F.S. Pump, Manual Call Point, F.S. Inlet |
| 17 Fire Shutter | Shutter, Detector |
| 18 Reserved | Nil |
| 19 Fixed Automatically Operated Approved Appliances | F.A.O.A.A. |
| 20 Fixed Form System| Foam |
| 21 Gas Detection System | G.D.S., Detector |
| 22 Gas Extraction System| G.E.S., Extraction Fan, Fan|
| 23 Hose Reel| H.R. |
| 24 Portable Fire Extinguisher | F.E. |
| 25 Portable Hand-operated Approved Appliance | Sand Basket, Fire Blanket|
| 26 Pressurization of Staircases | S.P.S., Supply Fan, Exhaust Fan, Fan, Damper |
| 27 Ring Main System with Fixed Pump | Fixed Pump, Pump, Hydrant Outlet |
| 28 Sprinkler System | Sprinkler, Flow Switch, S.S., Sprinkler Pump, Jockey Pump|
| 29 Static Smoke Extraction System | Extraction Fan, Fan|
| 30 Supply Tank| Tank |
| 31 Ventilation/Air Conditioning Control System | Vent, V.A.C., Mechanical Ventilation System |
| 32 Water Spray System | Water Tank |
| 33 Water Supply | Tank, Pump |
| 34 Street Fire Hydrant System | Pump, Hydrant|
| 35 Other| Nil|

**sample output format**
{
	"fsi_type": "32, 33"
}
""" 
STEP3_PROMPT = """
##task
extract the date on the fire service stamp from the image

##step
1. look for the fire service stamp
2. look for the date format as DDMonYY i.e. 1 Feb 1911
3. return the date  in json , use key name "date_on_stamp"

##notice
1. leave empty when fire service stamp not found
2. the date on fire service stamp are format as DDMonYY i.e. 1 Feb 1911

##stamp characteristics
1. the fire service stamp should have words like "Fire Service Requirements Incorporated", "Fire Service Installation"

**sample output format 1 **
{
	"date_on_stamp": "1-Feb-1911"
}
**sample output format 2 **
{
	"date_on_stamp": ""
}
"""  # Prompt for extracting date

async def async_process_image(image_data, prompt):
    loop = asyncio.get_running_loop()
    with ThreadPoolExecutor() as pool:
        result = await loop.run_in_executor(
            pool, 
            lambda: process_image(image_data, prompt)
        )
    return result

def process_pdf(pdf_data: BytesIO) -> BytesIO:
    """
    Extract the first page of a PDF and convert it to an image.
    
    Args:
        pdf_data: Binary PDF data
    
    Returns:
        BytesIO object containing the image data
    """
    try:
        # Open the PDF from binary data
        pdf_document = fitz.open(stream=pdf_data.getvalue(), filetype="pdf")
        
        # Check if PDF has pages
        if pdf_document.page_count == 0:
            raise ValueError("PDF has no pages")
            
        # Get the first page
        first_page = pdf_document[0]
        
        # Convert to image (using a higher resolution for better quality)
        pix = first_page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
        
        # Convert to PIL Image format
        img_data = BytesIO(pix.tobytes(output="jpeg"))
        
        return img_data
        
    except Exception as e:
        raise ValueError(f"Error processing PDF: {str(e)}")

async def process_file(file: UploadFile) -> BytesIO:
    """
    Process uploaded file and convert to image data if needed.
    
    Args:
        file: The uploaded file (image or PDF)
    
    Returns:
        BytesIO object containing image data
    """
    content_type = file.content_type
    file_data = await file.read()
    file_io = BytesIO(file_data)
    
    # Handle based on content type
    if content_type.startswith("image/"):
        return file_io
    elif content_type == "application/pdf":
        return process_pdf(file_io)
    else:
        raise HTTPException(
            status_code=400,
            detail="Unsupported file type. Please upload an image or PDF."
        )

@app.post("/vlm")
async def call_vlm(file: UploadFile = File(...), prompt: str = Form(...)) -> Dict[str, Any]:
    """
    Call VLM with image or PDF
    
    Args:
        file: The image or PDF file to process
        prompt: The prompt to send to the VLM
    
    Returns:
        Complete JSON with all fields
    """
    try:
        # Process the file to get image data
        image_data = await process_file(file)
        
        # Process the image and extract JSON
        result = process_image(image_data, prompt)
        
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing file: {str(e)}"
        )

@app.post("/process")
async def process_all(image: UploadFile = File(...)) -> Dict[str, Any]:
    """
    Process all information at once from image or PDF
    
    Args:
        file: The image or PDF file to process
    
    Returns:
        Complete JSON with all fields
    """
    try:
        # Process the file to get image data
        image_data = await process_file(image)
        
        # Process with all prompts concurrently
        step1_task = asyncio.create_task(async_process_image(BytesIO(image_data.getvalue()), STEP1_PROMPT))
        step2_task = asyncio.create_task(async_process_image(BytesIO(image_data.getvalue()), STEP2_PROMPT))
        step3_task = asyncio.create_task(async_process_image(BytesIO(image_data.getvalue()), STEP3_PROMPT))
        
        step1_result, step2_result, step3_result = await asyncio.gather(step1_task, step2_task, step3_task)

        # Detect incomplete parts for the Remarks field
        has_incomplete_parts = detect_incomplete_parts(BytesIO(image_data.getvalue()))

        # Check if date_on_stamp is missing
        date_on_stamp = step3_result.get("date_on_stamp", "")
        has_missing_date_stamp = not date_on_stamp or date_on_stamp.strip() == ""

        # Build remarks based on conditions
        remarks = []
        if has_incomplete_parts:
            remarks.append("incomplete")
        if has_missing_date_stamp:
            remarks.append("Missing Date Stamp")

        # Combine results
        combined_result = {
            "project_title": step1_result.get("project_title", ""),
            "drawing_title": step1_result.get("drawing_title", ""),
            "drawing_number": step1_result.get("drawing_number", ""),
            "fsi_type": step2_result.get("fsi_type", ""),
            "date_on_stamp": date_on_stamp,
            "DrawingRemark": ", ".join(remarks) if remarks else ""
        }

        return combined_result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing file: {str(e)}"
        )

@app.get("/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment variable or use 8000 as default
    port = int(os.getenv("PORT", 8000))
    
    uvicorn.run("new_api:app", host="0.0.0.0", port=port, reload=True)

