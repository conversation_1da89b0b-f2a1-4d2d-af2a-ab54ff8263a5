# Migration Guide: Image Storage Feature

This guide helps you migrate to the new version of the FSD API that includes image storage functionality.

## What's New

### Version 1.1.0 Features
- ✅ **Image Storage**: Save converted PDF images to disk
- ✅ **Configurable Paths**: Set custom storage locations
- ✅ **Per-Request Control**: Override settings per API call
- ✅ **Backward Compatibility**: All existing code continues to work
- ✅ **Configuration Endpoint**: New `/config` endpoint for settings

## Breaking Changes

**None!** This update is fully backward compatible.

## New Response Fields

When images are saved, responses now include:
```json
{
  "project_title": "...",
  "drawing_title": "...",
  "drawing_number": "...",
  "fsi_type": "...",
  "date_on_stamp": "...",
  "DrawingRemark": "...",
  "saved_image_path": "/path/to/saved/image.jpg"  // NEW FIELD
}
```

## Configuration Options

### Environment Variables (Optional)

Add to your `.env` file:
```env
# Enable image saving by default (default: false)
SAVE_IMAGES=true

# Set default storage path (default: ./saved_images)
IMAGE_STORAGE_PATH=/your/custom/path
```

### New API Parameters (Optional)

Both `/process` and `/vlm` endpoints now accept:
- `save_image` (boolean): Enable/disable saving for this request
- `storage_path` (string): Custom path for this request

## Migration Steps

### Step 1: Update Dependencies (if needed)
No new dependencies required. The existing `new_requirements.txt` is sufficient.

### Step 2: Update Environment (Optional)
If you want to enable image saving by default:
```bash
echo "SAVE_IMAGES=true" >> .env
echo "IMAGE_STORAGE_PATH=./converted_images" >> .env
```

### Step 3: Update Client Code (Optional)
If you want to use the new features:

#### Before (still works):
```python
response = requests.post("http://localhost:8000/process", 
                        files={'image': open('document.pdf', 'rb')})
```

#### After (with image saving):
```python
response = requests.post("http://localhost:8000/process", 
                        files={'image': open('document.pdf', 'rb')},
                        data={'save_image': 'true'})

result = response.json()
if 'saved_image_path' in result:
    print(f"Image saved to: {result['saved_image_path']}")
```

### Step 4: Test the Migration
Use the provided test script:
```bash
python test_image_storage.py
```

## Common Use Cases

### Case 1: Keep Current Behavior
Do nothing! Your existing code continues to work exactly as before.

### Case 2: Enable Saving for All Requests
Set environment variables:
```env
SAVE_IMAGES=true
IMAGE_STORAGE_PATH=./images
```

### Case 3: Save Only Specific Requests
Keep `SAVE_IMAGES=false` and use per-request parameters:
```python
# Save this one
requests.post(url, files=files, data={'save_image': 'true'})

# Don't save this one (default behavior)
requests.post(url, files=files)
```

### Case 4: Different Storage Paths
```python
# Save to project-specific folder
requests.post(url, files=files, data={
    'save_image': 'true',
    'storage_path': f'./projects/{project_id}/images'
})
```

## Rollback Plan

If you need to rollback:
1. Remove or set `SAVE_IMAGES=false` in your `.env`
2. Remove any `save_image` parameters from API calls
3. The API will behave exactly as before

## Monitoring

### Check Configuration
```bash
curl http://localhost:8000/config
```

### Check Health
```bash
curl http://localhost:8000/health
```

## Performance Considerations

- Image saving adds minimal overhead (< 50ms typically)
- Disk space usage depends on image size and volume
- No impact on processing speed or accuracy
- Storage directory is created automatically

## Security Notes

- Ensure storage directories have appropriate permissions
- Consider implementing cleanup policies for old images
- File paths in responses are server-side absolute paths

## Support

If you encounter issues:
1. Check the `/config` endpoint for current settings
2. Verify storage directory permissions
3. Test with the provided `test_image_storage.py` script
4. Check server logs for detailed error messages
