# Image Storage Feature

This document explains the new image storage functionality added to the FSD API.

## Overview

The API now supports saving converted images from PDFs to disk, allowing clients to avoid repeated PDF-to-image conversions. This feature is configurable and maintains full backward compatibility.

## Configuration

### Environment Variables

Configure image storage using environment variables in your `.env` file:

```env
# Enable/disable image saving globally (default: false)
SAVE_IMAGES=true

# Directory to save images (default: ./saved_images)
IMAGE_STORAGE_PATH=/path/to/your/images
```

### Per-Request Configuration

You can override global settings on a per-request basis using form parameters:

- `save_image`: Boolean to enable/disable saving for this request
- `storage_path`: Custom path for this request

## API Endpoints

### POST /process

Process all information from an image or PDF with optional image saving.

**Parameters:**
- `image` (file): The image or PDF file to process
- `save_image` (optional, boolean): Override global save setting
- `storage_path` (optional, string): Override global storage path

**Response:**
```json
{
  "project_title": "...",
  "drawing_title": "...",
  "drawing_number": "...",
  "fsi_type": "...",
  "date_on_stamp": "...",
  "DrawingRemark": "...",
  "saved_image_path": "/path/to/saved/image.jpg"
}
```

### POST /vlm

Process with custom prompt and optional image saving.

**Parameters:**
- `file` (file): The image or PDF file to process
- `prompt` (string): Custom prompt for the VLM
- `save_image` (optional, boolean): Override global save setting
- `storage_path` (optional, string): Override global storage path

**Response:**
```json
{
  "extracted_data": "...",
  "DrawingRemark": "...",
  "saved_image_path": "/path/to/saved/image.jpg"
}
```

## File Naming Convention

Saved images use the following naming pattern:
```
{original_name}_{timestamp}_{unique_id}.{extension}
```

Example: `floor_plan_20250102_143022_a1b2c3d4.jpg`

## Backward Compatibility

- All existing API calls work unchanged
- The `saved_image_path` field is only added when images are actually saved
- Default behavior (no saving) remains the same unless explicitly configured

## Usage Examples

### Enable saving globally
```env
SAVE_IMAGES=true
IMAGE_STORAGE_PATH=./converted_images
```

### Save for specific request only
```bash
curl -X POST "http://localhost:8000/process" \
  -F "image=@document.pdf" \
  -F "save_image=true" \
  -F "storage_path=/custom/path"
```

### Use custom storage path
```bash
curl -X POST "http://localhost:8000/process" \
  -F "image=@document.pdf" \
  -F "storage_path=/project/images"
```

## Benefits

1. **Reduced Processing Time**: Clients can reuse saved images instead of converting PDFs repeatedly
2. **Storage Flexibility**: Configurable storage paths for different use cases
3. **Backward Compatible**: Existing integrations continue to work without changes
4. **Optional Feature**: Can be disabled entirely if not needed
5. **Unique Naming**: Prevents file conflicts with timestamp and UUID-based naming

## Security Considerations

- Ensure the storage directory has appropriate permissions
- Consider disk space management for high-volume usage
- The API creates directories automatically but doesn't clean up old files
- File paths in responses are absolute paths on the server
